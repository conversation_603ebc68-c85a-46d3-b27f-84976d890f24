from fastapi import API<PERSON>out<PERSON>, Depends, HTTPException
from core.security import create_access_token, verify_password, get_tenant_info
from core.database import get_tenant_id_and_name_from_slug, get_async_db_from_tenant_id
from core.helper.mongo_helper import convert_objectid_to_str
from models.security import OAuth2PasswordRequestFormWithClientID
from models.user import UserTenantDB
from datetime import timedelta
import logging

logger = logging.getLogger(__name__)
router = APIRouter(tags=["Authentication"])

@router.post("/login")
async def login(form_data: OAuth2PasswordRequestFormWithClientID = Depends()):
    """
    Login endpoint that authenticates users and returns JWT access token
    """
    # Find the database name of that tenant
    try:
        result = get_tenant_id_and_name_from_slug(form_data.client_id)

        tenant_id = str(result["_id"])
        tenant_database = get_async_db_from_tenant_id(tenant_id)

        # Connect to the user_collection of that database
        user = await tenant_database.users.find_one({"username": form_data.username})

        if not user:
            logger.error(f"User not found: {form_data.username}")
            raise HTTPException(status_code=401, detail="User not found")

        if not verify_password(form_data.password, user["hashed_password"]):
            logger.error(f"Incorrect credentials: {form_data.username}")
            raise HTTPException(status_code=401, detail="Incorrect credentials")

        # Create access token with user information (same as main.py)
        access_token = create_access_token(
            data={"sub": user["username"], "role": user["role"], "tenant_id": tenant_id},
            expires_delta=timedelta(days=365)
        )

        # Convert ObjectId to string for JSON response
        user = convert_objectid_to_str(user)

        logger.info(f"User logged in: {user['username']} for tenant: {result['name']}")

        return {
            "id": user["_id"],
            "access_token": access_token,
            "token_type": "bearer",
            "username": user['username'],
            "role": user['role'],
            "tenant_id": tenant_id,
            "tenant_label": result["name"],
            "tenant_slug": form_data.client_id,
        }
    except Exception as e:
        logger.error(f"Login error: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Login failed: {str(e)}")

@router.get("/verify-token")
async def verify_token(user_tenant_info: UserTenantDB = Depends(get_tenant_info)):
    """
    Verify if the provided token is valid and return user information
    """
    return {
        "valid": True,
        "user": user_tenant_info.user.model_dump(),
        "tenant_id": user_tenant_info.tenant_id
    }