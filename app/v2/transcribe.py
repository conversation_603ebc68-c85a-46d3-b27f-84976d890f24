from fastapi import APIRouter, Depends, HTTPException, File, UploadFile
from fastapi.responses import JSONResponse
from core.security import get_tenant_info
import logging
import os
logger = logging.getLogger(__name__)



router = APIRouter(prefix="/transcribe", tags=["Transcription"])
@router.post("")
async def transcribe(file: UploadFile = File(...), user_tenant_info = Depends(get_tenant_info)):
    """
    Endpoint that does transcription.
    Requires authentication via Bear<PERSON> token.
    """
    logger.info("Received transcription request")
    logger.info(f"File: {file.filename}")

    try:
        # Read the file content
        file_content = await file.read()

        # Create multipart form data for the transcription server
        import httpx
        files = {
            "file": (file.filename, file_content, "audio/wav")
        }

        # Call the transcription server's endpoint
        async with httpx.AsyncClient() as client:
            response = await client.post(
                f"{os.getenv('TRANSCRIPTION_API_URL', 'http://*************:8005')}/transcribe/",
                files=files,
                timeout=240  # Long timeout for large audio files
            )
            response.raise_for_status()

            transcription_result = response.json()
            time_stamps = transcription_result
            
            return JSONResponse(content={"word_timestamps": time_stamps["word_timestamps"]})


    except httpx.HTTPStatusError as e:
        logger.error(f"Transcription server error: {e.response.text}")
        raise HTTPException(
            status_code=e.response.status_code,
            detail=f"Transcription API error: {e.response.text}"
        )
    except httpx.RequestError as e:
        logger.error(f"Could not connect to GPU server: {str(e)}")
        raise HTTPException(
            status_code=503,
            detail=f"Service unavailable: Unable to connect to transcription API - {str(e)}"
        )
    except Exception as e:
        logger.error(f"Transcription error: {str(e)}")
        raise HTTPException(
            status_code=500, 
            detail=f"Transcription failed: {str(e)}")